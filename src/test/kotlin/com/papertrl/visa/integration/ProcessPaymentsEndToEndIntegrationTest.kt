package com.papertrl.visa.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.incoming.visa.ProcessPaymentsUseCase
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ProcessPaymentsRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiRequestJpaRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiResponseJpaRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.math.BigDecimal

/**
 * True end-to-end integration tests for ProcessPayments without any mocks.
 * Tests the complete payment process flow using real implementations of all dependencies.
 * Uses the actual VisaApiClient implementation to test the full process flow.
 */
@SpringBootTest
@ActiveProfiles("test")
class ProcessPaymentsEndToEndIntegrationTest {

    private val logger = LoggerFactory.getLogger(ProcessPaymentsEndToEndIntegrationTest::class.java)

    @Autowired
    private lateinit var processPaymentsUseCase: ProcessPaymentsUseCase

    @Autowired
    private lateinit var processPaymentsRepository: ProcessPaymentsRepository

    @Autowired
    private lateinit var apiRequestJpaRepository: ApiRequestJpaRepository

    @Autowired
    private lateinit var apiResponseJpaRepository: ApiResponseJpaRepository

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @BeforeEach
    fun setUp() = runBlocking {
        logger.info("Starting ProcessPayments integration test setup")

        logger.debug("Truncating all database tables...")

        // Truncate all tables (in correct order due to foreign key constraints)
        apiResponseJpaRepository.deleteAll()
        apiRequestJpaRepository.deleteAll()
        processPaymentsRepository.deleteAll()

        logger.debug("All database tables truncated successfully")

        val processPaymentsCount = processPaymentsRepository.findAll().size
        val apiRequestCount = apiRequestJpaRepository.count()
        val apiResponseCount = apiResponseJpaRepository.count()

        logger.debug("Table counts after truncation: process_payments_request={}, api_request={}, api_response={}",
            processPaymentsCount, apiRequestCount, apiResponseCount)

        logger.info("ProcessPayments integration test setup completed")
    }


    @Test
    @DisplayName("Should execute complete payment process flow with real VisaApiClient")
    fun `should execute complete payment process flow with real implementations`() = runBlocking {
        // Given - Step 1: Create a valid payment request
        val inboundRequest = createValidRequest()

        // When - Execute the complete payment process flow through real Spring Boot context
        // This uses real implementations of all dependencies:
        // - Real ProcessPaymentsDomainService
        // - Real ProcessPaymentsRepositoryAdapter with PostgreSQL database
        // - Real VisaStatusCodeEvaluator
        // - Real VisaApiClientAdapter (will make actual HTTP calls)

        try {
            val result = processPaymentsUseCase.processPayment(inboundRequest)

            logger.info("ProcessPayments use case executed with status: {}", result.status)
            logger.debug("ProcessPayments result: messageId={}, status={}, databaseId={}",
                result.messageId, result.status, result.id)

            // Then - Verify the complete flow worked correctly
            assertNotNull(result, "Result should not be null")
            assertNotNull(result.id, "Should have database-generated ID")

            // Verify original request data is preserved
            assertEquals(inboundRequest.messageId, result.messageId)
            assertEquals(inboundRequest.paymentGrossAmount, result.paymentGrossAmount)
            assertEquals(inboundRequest.supplierName, result.supplierName)
            assertEquals(inboundRequest.supplierID, result.supplierID)
            assertEquals(inboundRequest.primaryEmailAddress, result.primaryEmailAddress)

            // Verify data was actually persisted in the database
            val savedRequests = processPaymentsRepository.findAll()
            assertEquals(1, savedRequests.size, "Should have exactly one saved request")

            val savedRequest = savedRequests.first()
            assertEquals(inboundRequest.messageId, savedRequest.messageId)
            assertNotNull(savedRequest.id)

            // Verify that processing completed (regardless of SUCCESS or FAILED status)
            // The key integration test success is that the flow completed and data was persisted
            assertTrue(
                savedRequest.status != VisaRequestStatus.PENDING,
                "Status should not be PENDING - processing should have completed"
            )

            // The actual status depends on the real Visa API response
            // Could be SUCCESS or FAILED depending on API availability and credentials
            // RETRY status is no longer used at domain level (handled at infrastructure level)
            assertTrue(
                savedRequest.status in listOf(
                    VisaRequestStatus.SUCCESS,
                    VisaRequestStatus.FAILED
                ),
                "Status should be either SUCCESS or FAILED"
            )

            logger.info("ProcessPayments integration test completed successfully with final status: {}", result.status)

        } catch (e: Exception) {
            // Expected behavior when Visa API is not available or returns error codes (like PP003)
            logger.info("Expected exception when testing with real Visa API: {}", e.message)

            // Verify that the request was still saved to database before the API call failed
            val savedRequests = processPaymentsRepository.findAll()
            assertTrue(savedRequests.isNotEmpty(), "ProcessPayments request should be saved to database even on API failure")

            val savedRequest = savedRequests.first()
            assertEquals(inboundRequest.messageId, savedRequest.messageId, "MessageId should match")
            assertNotNull(savedRequest.id, "Should have database-generated ID")
            assertEquals(VisaRequestStatus.FAILED, savedRequest.status, "Status should be FAILED due to API error")
            assertNotNull(savedRequest.failureReason, "Should have failure reason")

            logger.debug("Request was properly saved to database before API failure")
            logger.debug("Saved request: messageId={}, status={}, failureReason={}",
                savedRequest.messageId, savedRequest.status, savedRequest.failureReason)

            // Test passes even if API call fails - this is expected in test environment
            // We've verified that:
            // 1. Database operations work correctly
            // 2. Error handling works as expected
            // 3. The system gracefully handles Visa API failures
            logger.info("ProcessPayments integration test completed successfully - verified error handling and database operations")
        }
    }

    /**
     * Creates a valid ProcessPaymentsRequest for testing using realistic production-like data.
     * Maps the nested JSON payload structure to the flat domain model fields.
     * Based on actual Visa API test data structure.
     */
    private fun createValidRequest(): ProcessPaymentsRequest {
        // Auto-generate messageId as per system requirements
        val messageId = "PP-${System.currentTimeMillis()}-${(1000..9999).random()}"

        // Realistic invoice data matching the test JSON payload structure
        val invoicesData = """[
            {
                "invoiceNumber": "INV01",
                "invoiceAmount": 100,
                "invoiceDate": "2017-02-01"
            },
            {
                "invoiceNumber": "INV02",
                "invoiceAmount": 200,
                "invoiceDate": "2017-03-01"
            }
        ]"""

        // Realistic alternate email addresses data
        val alternateEmailAddressesData = """["<EMAIL>"]"""

        return ProcessPaymentsRequest(
            messageId = messageId,
            idempotencyKey = "test-idempotency-${System.currentTimeMillis()}-${(1000..9999).random()}",
            tenantId = "TEST_TENANT",
            actionType = 1,
            paymentExpiryDate = "2026-06-30",
            accountType = 2,
            currencyCode = "USD",
            paymentGrossAmount = BigDecimal("300"),
            paymentType = "STP",
            supplierName = "TestVPASupplier111111",
            supplierID = 111111L,
            supplierAddressLine1 = "Address1",
            supplierAddressLine2 = "Address2",
            supplierCity = "Austin",
            supplierState = "TX",
            supplierCountryCode = "USA",
            supplierPostalCode = "78759",
            primaryEmailAddress = "<EMAIL>",
            emailNotes = "Email Notes",
            invoicesData = objectMapper.readTree(invoicesData),
            alternateEmailAddressesData = objectMapper.readTree(alternateEmailAddressesData)
        )
    }

}
